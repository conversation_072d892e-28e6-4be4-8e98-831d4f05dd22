// 打印机状态管理模块

class PrinterManager {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.isConnecting = false;
        this.lastStatus = null;
        
        // DOM元素引用
        this.elements = {
            printerState: document.getElementById('printer-state'),
            printProgress: document.getElementById('print-progress'),
            printFilename: document.getElementById('print-filename'),
            extruderTemp: document.getElementById('extruder-temp'),
            extruderTarget: document.getElementById('extruder-target'),
            bedTemp: document.getElementById('bed-temp'),
            bedTarget: document.getElementById('bed-target'),
            printProgressBar: document.getElementById('print-progress-bar'),
            progressPercentage: document.getElementById('progress-percentage'),
            extruderProgress: document.getElementById('extruder-progress'),
            bedProgress: document.getElementById('bed-progress'),
            macroStatusGrid: document.getElementById('macro-status-grid')
        };

        // 宏状态缓存
        this.macroStatus = [];
    }

    // 初始化
    init() {
        console.log('初始化PrinterManager...');
        this.setupEventListeners();

        // 立即请求初始状态，不等WebSocket连接
        this.requestInitialStatus();

        // 然后建立WebSocket连接
        this.connectWebSocket();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 页面可见性变化时重连
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && (!this.ws || this.ws.readyState !== WebSocket.OPEN)) {
                console.log('页面重新可见，尝试重连WebSocket...');
                this.connectWebSocket();
            }
        });
    }

    // 连接WebSocket
    connectWebSocket() {
        if (this.isConnecting) {
            return;
        }

        this.isConnecting = true;
        const wsUrl = `ws://${window.location.host}/ws/printer`;
        
        console.log('连接WebSocket:', wsUrl);
        if (window.AppUtils && window.AppUtils.updateConnectionStatus) {
            window.AppUtils.updateConnectionStatus('connecting');
        }

        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = (event) => {
                console.log('WebSocket连接成功');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                if (window.AppUtils && window.AppUtils.updateConnectionStatus) {
                    window.AppUtils.updateConnectionStatus('connected');
                }
                
                // 请求当前状态
                this.requestStatus();
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleStatusUpdate(data);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log('WebSocket连接关闭:', event.code, event.reason);
                this.isConnecting = false;
                if (window.AppUtils && window.AppUtils.updateConnectionStatus) {
                    window.AppUtils.updateConnectionStatus('disconnected');
                }
                
                // 自动重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.scheduleReconnect();
                } else {
                    console.log('达到最大重连次数，停止重连');
                    if (window.AppUtils && window.AppUtils.showError) {
                        window.AppUtils.showError('无法连接到服务器，请检查网络连接');
                    }
                }
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.isConnecting = false;
                if (window.AppUtils && window.AppUtils.updateConnectionStatus) {
                    window.AppUtils.updateConnectionStatus('disconnected');
                }
            };

        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.isConnecting = false;
            if (window.AppUtils && window.AppUtils.updateConnectionStatus) {
                window.AppUtils.updateConnectionStatus('disconnected');
            }
            this.scheduleReconnect();
        }
    }

    // 计划重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
        
        console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
        
        setTimeout(() => {
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                this.connectWebSocket();
            }
        }, delay);
    }

    // 请求状态
    requestStatus() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send('get_status');
        }
    }

    // 请求初始状态（HTTP API备用）
    async requestInitialStatus() {
        try {
            console.log('正在获取初始状态...');

            // 显示加载状态
            this.showLoadingState();

            const response = await fetch('/api/printer/status');
            if (response.ok) {
                const data = await response.json();
                console.log('获取到初始状态:', data);
                this.handleStatusUpdate(data);
            } else {
                console.error('HTTP请求失败:', response.status);
            }
        } catch (error) {
            console.error('获取初始状态失败:', error);
        }
    }

    // 显示加载状态
    showLoadingState() {
        if (window.AppUtils && window.AppUtils.safeSetText) {
            window.AppUtils.safeSetText('printer-state', '加载中...');
            window.AppUtils.safeSetText('print-progress', '加载中...');
            window.AppUtils.safeSetText('print-filename', '加载中...');
            window.AppUtils.safeSetText('extruder-temp', '--');
            window.AppUtils.safeSetText('extruder-target', '--');
            window.AppUtils.safeSetText('bed-temp', '--');
            window.AppUtils.safeSetText('bed-target', '--');
        }
    }

    // 处理状态更新
    handleStatusUpdate(data) {
        console.log('收到状态更新:', data);

        // 更新打印机状态
        this.updatePrinterState(data.state);

        // 更新打印进度
        this.updatePrintProgress(data.progress, data.filename);

        // 更新温度
        if (data.temperatures) {
            this.updateTemperatures(data.temperatures);
        }

        // 更新宏状态
        if (data.macros) {
            this.updateMacroStatus(data.macros);
        }

        // 更新最后更新时间
        if (window.AppUtils && window.AppUtils.setLastUpdateTime) {
            window.AppUtils.setLastUpdateTime();
        }

        // 保存状态
        this.lastStatus = data;
    }

    // 状态翻译映射
    getStateText(state) {
        const stateMap = {
            'standby': '待机',
            'printing': '打印中',
            'paused': '暂停',
            'error': '错误',
            'complete': '完成',
            'cancelled': '已取消',
            'ready': '就绪',
            'offline': '离线',
            'unknown': '未知'
        };
        return stateMap[state] || state;
    }

    // 更新打印机状态
    updatePrinterState(state) {
        const chineseState = this.getStateText(state || 'unknown');
        if (window.AppUtils && window.AppUtils.safeSetText) {
            window.AppUtils.safeSetText('printer-state', chineseState);
        }

        // 移除所有状态类
        const stateElement = this.elements.printerState;
        if (stateElement) {
            stateElement.classList.remove('state-standby', 'state-printing', 'state-paused', 'state-error', 'state-complete');

            // 根据状态添加对应的类
            const stateClass = this.getStateClass(state);
            stateElement.classList.add(stateClass);
        }
    }

    // 获取状态对应的CSS类
    getStateClass(state) {
        const stateMap = {
            'standby': 'state-standby',
            'printing': 'state-printing',
            'paused': 'state-paused',
            'error': 'state-error',
            'complete': 'state-complete',
            'ready': 'state-standby',
            'idle': 'state-standby',
            'unknown': 'state-standby'
        };

        return stateMap[state] || 'state-standby';
    }

    // 更新打印进度
    updatePrintProgress(progress, filename) {
        if (window.AppUtils && window.AppUtils.formatPercentage) {
            const percentage = window.AppUtils.formatPercentage(progress);

            if (window.AppUtils.safeSetText) {
                window.AppUtils.safeSetText('print-progress', percentage);
                window.AppUtils.safeSetText('print-filename', filename || '无文件');
            }

        // 更新进度条
        const progressWidth = `${(progress || 0) * 100}%`;
            if (window.AppUtils.safeSetStyle) {
                window.AppUtils.safeSetStyle('print-progress-bar', 'width', progressWidth);
            }
        }

        // 添加打印中的动画效果
        const progressContainer = document.querySelector('.progress-container');
        if (progressContainer) {
            if (progress > 0 && progress < 1) {
                progressContainer.classList.add('printing');
            } else {
                progressContainer.classList.remove('printing');
            }
        }
    }

    // 更新温度
    updateTemperatures(temperatures) {
        if (temperatures.extruder && window.AppUtils) {
            const ext = temperatures.extruder;
            if (window.AppUtils.safeSetText && window.AppUtils.formatNumber) {
                window.AppUtils.safeSetText('extruder-temp', window.AppUtils.formatNumber(ext.current));
                window.AppUtils.safeSetText('extruder-target', window.AppUtils.formatNumber(ext.target));
            }

            // 检查是否正在加热并应用动画
            this.updateHeatingAnimation('extruder-temp', ext.current, ext.target);

            // 更新喷嘴温度进度条
            if (window.AppUtils.safeSetStyle) {
            if (ext.target > 0) {
                const progress = Math.min((ext.current / ext.target) * 100, 100);
                    window.AppUtils.safeSetStyle('extruder-progress', 'width', `${progress}%`);
            } else {
                // 目标温度为0时，清空进度条
                    window.AppUtils.safeSetStyle('extruder-progress', 'width', '0%');
                }
            }
        }

        if (temperatures.bed && window.AppUtils) {
            const bed = temperatures.bed;
            if (window.AppUtils.safeSetText && window.AppUtils.formatNumber) {
                window.AppUtils.safeSetText('bed-temp', window.AppUtils.formatNumber(bed.current));
                window.AppUtils.safeSetText('bed-target', window.AppUtils.formatNumber(bed.target));
            }

            // 检查是否正在加热并应用动画
            this.updateHeatingAnimation('bed-temp', bed.current, bed.target);

            // 更新热床温度进度条
            if (window.AppUtils.safeSetStyle) {
            if (bed.target > 0) {
                const progress = Math.min((bed.current / bed.target) * 100, 100);
                    window.AppUtils.safeSetStyle('bed-progress', 'width', `${progress}%`);
            } else {
                // 目标温度为0时，清空进度条
                    window.AppUtils.safeSetStyle('bed-progress', 'width', '0%');
                }
            }
        }
    }

    // 更新加热动画效果
    updateHeatingAnimation(elementId, currentTemp, targetTemp) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // 判断是否正在加热：有目标温度就是在加热（与后端逻辑保持一致）
        const isHeating = targetTemp > 0;

        if (isHeating) {
            element.classList.add('heating');
        } else {
            element.classList.remove('heating');
        }
    }

    // ==================== 宏状态管理方法 ====================

    // 更新宏状态显示
    updateMacroStatus(macros) {
        console.log('更新宏状态:', macros);

        // 保存宏状态到缓存
        this.macroStatus = macros || [];

        // 渲染宏状态到页面
        this.renderMacroStatus();
    }

    // 渲染宏状态到页面
    renderMacroStatus() {
        const grid = this.elements.macroStatusGrid;
        const section = document.getElementById('macro-status-section');

        if (!grid || !section) {
            console.warn('宏状态元素未找到');
            return;
        }

        // 清空现有内容
        grid.innerHTML = '';

        // 如果没有宏配置，隐藏整个区域
        if (!this.macroStatus || this.macroStatus.length === 0) {
            section.style.display = 'none';
            return;
        }

        // 有宏配置时显示区域
        section.style.display = 'block';

        // 渲染每个宏状态
        this.macroStatus.forEach(macro => {
            const macroItem = this.createMacroStatusItem(macro);
            grid.appendChild(macroItem);
        });
    }

    // 创建宏状态项
    createMacroStatusItem(macro) {
        const textElement = document.createElement('span');
        textElement.className = `macro-status-text ${macro.currentStatus === 'running' ? 'running' : 'idle'}`;
        textElement.setAttribute('data-macro-id', macro.id);

        const statusText = this.getMacroStatusText(macro.currentStatus);
        textElement.innerHTML = `
            <span class="macro-name">${macro.displayName}</span><span class="status-separator">:</span><span class="status-text">${statusText}</span>
        `;

        return textElement;
    }

    // 获取宏状态文本
    getMacroStatusText(status) {
        const statusMap = {
            'idle': '空闲',
            'running': '执行中'
        };
        return statusMap[status] || status;
    }

    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// 创建全局实例
const printerManager = new PrinterManager();

// 导出类和实例到全局作用域
window.PrinterManagerClass = PrinterManager;  // 导出类
window.PrinterManager = printerManager;       // 导出实例

// 确保在页面加载完成后可以访问
console.log('PrinterManager模块已加载，实例:', printerManager);
