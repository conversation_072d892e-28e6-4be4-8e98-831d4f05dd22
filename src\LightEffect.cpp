#include "LightEffect.h"
#include "Config.h"

// 初始化静态成员变量
const std::map<EffectType, std::vector<EffectParamConfig>> LightEffect::EFFECT_PARAM_MAP = LightEffect::initEffectParamMap();
const std::map<EffectType, EffectInfo> LightEffect::EFFECT_INFO_MAP = LightEffect::initEffectInfoMap();

/**
 * 初始化灯效参数配置映射表
 */
std::map<EffectType, std::vector<EffectParamConfig>> LightEffect::initEffectParamMap() {
    std::map<EffectType, std::vector<EffectParamConfig>> paramMap;
    
    // 呼吸灯参数配置
    paramMap[EffectType::BREATHING] = {
        EffectParamConfig(EffectParamType::BRIGHTNESS, "亮度", 1, 255, LED_BRIGHTNESS_DEFAULT, ""),
        EffectParamConfig(EffectParamType::SPEED, "速度", 1, 100, LED_SPEED_DEFAULT, ""),
        EffectParamConfig(EffectParamType::COLOR, "颜色", 0, 16777215, 16711680, "")  // 默认红色
    };
    
    // 彩虹参数配置
    paramMap[EffectType::RAINBOW] = {
        EffectParamConfig(EffectParamType::BRIGHTNESS, "亮度", 1, 255, LED_BRIGHTNESS_DEFAULT, ""),
        EffectParamConfig(EffectParamType::SPEED, "速度", 1, 100, LED_SPEED_DEFAULT, ""),
        EffectParamConfig(EffectParamType::DIRECTION, "方向", 0, 1, 0, ""),
        EffectParamConfig(EffectParamType::INTENSITY, "强度", 1, 100, LED_INTENSITY_DEFAULT, "")
    };
    
    // 跑马灯参数配置
    paramMap[EffectType::RUNNING] = {
        EffectParamConfig(EffectParamType::BRIGHTNESS, "亮度", 1, 255, LED_BRIGHTNESS_DEFAULT, ""),
        EffectParamConfig(EffectParamType::SPEED, "速度", 1, 100, LED_SPEED_DEFAULT, ""),
        EffectParamConfig(EffectParamType::COLOR, "颜色", 0, 16777215, 16711680, ""),  // 默认红色
        EffectParamConfig(EffectParamType::DIRECTION, "方向", 0, 1, 0, ""),
        EffectParamConfig(EffectParamType::INTENSITY, "强度", 1, 100, LED_INTENSITY_DEFAULT, "")
    };

    // 温度映射参数配置
    paramMap[EffectType::TEMPERATURE_MAP] = {
        EffectParamConfig(EffectParamType::BRIGHTNESS, "亮度", 1, 255, LED_BRIGHTNESS_DEFAULT, ""),
        EffectParamConfig(EffectParamType::COLOR, "颜色", 0, 16777215, 16711680, ""),  // 默认红色
        EffectParamConfig(EffectParamType::DIRECTION, "方向", 0, 1, 0, "")
    };

    return paramMap;
}

/**
 * 初始化灯效信息映射表
 */
std::map<EffectType, EffectInfo> LightEffect::initEffectInfoMap() {
    std::map<EffectType, EffectInfo> infoMap;
    
    // 呼吸灯信息，信息提示不可超过28个字
    infoMap.emplace(EffectType::BREATHING, EffectInfo(
        EffectType::BREATHING,
        "呼吸灯",
        "柔和的呼吸效果，亮度周期性变化，可设置多个颜色",
        EFFECT_PARAM_MAP.at(EffectType::BREATHING)
    ));
    
    // 彩虹信息
    infoMap.emplace(EffectType::RAINBOW, EffectInfo(
        EffectType::RAINBOW,
        "彩虹",
        "流动的彩虹色彩效果，强度控制彩虹长度",
        EFFECT_PARAM_MAP.at(EffectType::RAINBOW)
    ));
    
    // 跑马灯信息
    infoMap.emplace(EffectType::RUNNING, EffectInfo(
        EffectType::RUNNING,
        "跑马灯",
        "移动的光点效果，强度控制拖尾长度比例，可设置多个颜色",
        EFFECT_PARAM_MAP.at(EffectType::RUNNING)
    ));

    // 温度映射信息
    infoMap.emplace(EffectType::TEMPERATURE_MAP, EffectInfo(
        EffectType::TEMPERATURE_MAP,
        "温度映射",
        "根据温度比例逐颗点亮LED，支持多颜色渐变显示加热进度",
        EFFECT_PARAM_MAP.at(EffectType::TEMPERATURE_MAP)
    ));

    return infoMap;
}

/**
 * 获取所有可用的灯效信息
 */
std::vector<EffectInfo> LightEffect::getAllEffectInfo() {
    std::vector<EffectInfo> effectList;
    
    for (const auto& pair : EFFECT_INFO_MAP) {
        effectList.push_back(pair.second);
    }
    
    return effectList;
}

/**
 * 根据灯效类型获取可用参数配置
 */
std::vector<EffectParamConfig> LightEffect::getEffectParams(EffectType effectType) {
    auto it = EFFECT_PARAM_MAP.find(effectType);
    if (it != EFFECT_PARAM_MAP.end()) {
        return it->second;
    }
    return std::vector<EffectParamConfig>();
}

/**
 * 获取灯效名称
 */
const char* LightEffect::getEffectName(EffectType effectType) {
    auto it = EFFECT_INFO_MAP.find(effectType);
    if (it != EFFECT_INFO_MAP.end()) {
        return it->second.name;
    }
    return "未知灯效";
}

/**
 * 验证参数值是否在有效范围内
 */
bool LightEffect::validateParams(EffectType effectType, const EffectParams& params) {
    auto paramConfigs = getEffectParams(effectType);
    
    for (const auto& config : paramConfigs) {
        int value = 0;
        
        switch (config.type) {
            case EffectParamType::BRIGHTNESS:
                value = params.brightness;
                break;
            case EffectParamType::SPEED:
                value = params.speed;
                break;
            case EffectParamType::DIRECTION:
                value = params.direction;
                break;
            case EffectParamType::INTENSITY:
                value = params.intensity;
                break;
            case EffectParamType::COLOR:
                // 验证颜色数组不为空
                if (params.colors.empty()) {
                    return false;
                }
                continue;
        }
        
        if (value < config.minValue || value > config.maxValue) {
            return false;
        }
    }
    
    return true;
}

// =================================================================
// 灯效渲染方法
// =================================================================

/**
 * 渲染呼吸灯效果
 */
void LightEffect::renderBreathing(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime) {
    if (!leds || startLed > endLed || startLed < 0 || params.colors.empty()) return;

    // 计算单个颜色的呼吸周期 (基于速度参数)
    unsigned long singleCycleTime = speedToInterval(params.speed) * 20; // 单个颜色的呼吸周期

    // 计算总周期时间（所有颜色的周期）
    unsigned long totalCycleTime = singleCycleTime * params.colors.size();

    // 计算当前在总周期中的位置
    unsigned long totalPhase = frameTime % totalCycleTime;

    // 计算当前应该显示的颜色索引
    int currentColorIndex = totalPhase / singleCycleTime;
    if (currentColorIndex >= params.colors.size()) {
        currentColorIndex = params.colors.size() - 1;
    }

    // 计算当前颜色的呼吸相位
    unsigned long colorPhase = totalPhase % singleCycleTime;

    // 修改呼吸波形：使用余弦波，让它从0开始（暗）
    // 0 -> PI: 从暗到亮
    // PI -> 2*PI: 从亮到暗
    float breathPhase = (float)colorPhase / singleCycleTime * 2.0 * PI;
    float breathFactor = (1.0 - cos(breathPhase)) / 2.0; // 0.0 到 1.0，从暗开始

    // 获取当前颜色
    CRGB currentColor = params.colors[currentColorIndex];

    // 计算最终颜色
    CRGB finalColor = applyBrightness(currentColor, params.brightness * breathFactor);

    // 填充LED
    for (int i = startLed; i <= endLed; i++) {
        leds[i] = finalColor;
    }


}

/**
 * 渲染彩虹灯效果
 */
void LightEffect::renderRainbow(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime) {
    if (!leds || startLed > endLed || startLed < 0) return;

    int ledCount = endLed - startLed + 1;
    unsigned long timeOffset = frameTime / (speedToInterval(params.speed) / 3); // 彩虹速度提升3倍

    for (int i = startLed; i <= endLed; i++) {
        // 计算色相值
        uint8_t hue;
        if (params.direction == 0) {
            // 正向
            hue = (timeOffset - (i - startLed) * 255 / ledCount * params.intensity / 100) % 256;
        } else {
            // 反向
            hue = (timeOffset + (i - startLed) * 255 / ledCount * params.intensity / 100) % 256;
        }

        // 生成HSV颜色并转换为RGB
        CHSV hsvColor(hue, 255, params.brightness);
        leds[i] = hsvColor;
    }
}

/**
 * 渲染跑马灯效果
 */
void LightEffect::renderRunning(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime) {
    if (!leds || startLed > endLed || startLed < 0 || params.colors.empty()) return;

    int ledCount = endLed - startLed + 1;
    unsigned long timeStep = frameTime / speedToInterval(params.speed);

    // 清空所有LED
    for (int i = startLed; i <= endLed; i++) {
        leds[i] = CRGB::Black;
    }

    // 计算拖尾长度
    int maxTailLength = constrainValue((ledCount * params.intensity) / 200, 1, ledCount / 2);

    // 计算总循环长度（LED数量 + 拖尾长度，确保拖尾完全消失）
    int totalCycleLength = ledCount + maxTailLength;

    // 计算当前颜色索引（基于完整的循环周期）
    int totalCycles = timeStep / totalCycleLength;
    int colorIndex = totalCycles % params.colors.size();
    CRGB currentColor = params.colors[colorIndex];

    // 计算当前位置
    int cyclePosition = timeStep % totalCycleLength;
    int position;

    if (params.direction == 0) {
        // 正向：从0开始向ledCount-1移动，然后继续移动maxTailLength步让拖尾消失
        position = cyclePosition;
    } else {
        // 反向：从ledCount-1开始向0移动，然后继续移动maxTailLength步让拖尾消失
        position = (ledCount - 1) - cyclePosition;
    }

    // 计算主光点的实际位置（可能超出LED范围）
    int mainLed = startLed + position;

    // 如果主光点在有效范围内，则渲染主光点
    if (position >= 0 && position < ledCount) {
        leds[mainLed] = applyBrightness(currentColor, params.brightness);
    }

    // 无论主光点是否在范围内，都渲染拖尾效果
    for (int tail = 1; tail <= maxTailLength; tail++) {
        int tailLed;
        if (params.direction == 0) {
            tailLed = mainLed - tail;
        } else {
            tailLed = mainLed + tail;
        }

        // 只要拖尾LED在有效范围内就渲染
        if (tailLed >= startLed && tailLed <= endLed) {
            // 计算拖尾亮度：距离主光点越远越暗
            uint8_t tailBrightness = params.brightness * (maxTailLength + 1 - tail) / (maxTailLength + 1) / 2;
            leds[tailLed] = applyBrightness(currentColor, tailBrightness);
        }
    }


}

/**
 * 渲染温度映射效果
 */
void LightEffect::renderTemperatureMap(CRGB* leds, int startLed, int endLed, const EffectParams& params,
                                      float currentTemp, float targetTemp, unsigned long frameTime) {
    if (!leds || startLed > endLed || startLed < 0 || params.colors.empty() || targetTemp <= 0) return;

    int ledCount = endLed - startLed + 1;

    // 计算温度比例 (0.0 到 1.0)
    float tempRatio = min(currentTemp / targetTemp, 1.0f);

    // 计算应该点亮的LED数量
    int litCount = (int)(tempRatio * ledCount);

    // 清空所有LED
    for (int i = startLed; i <= endLed; i++) {
        leds[i] = CRGB::Black;
    }

    // 根据方向点亮LED，颜色渐变映射在整个分段上
    for (int i = 0; i < litCount; i++) {
        int ledIndex;
        if (params.direction == 0) {
            // 正向：从startLed开始点亮
            ledIndex = startLed + i;
        } else {
            // 反向：从endLed开始点亮
            ledIndex = endLed - i;
        }

        if (ledIndex >= startLed && ledIndex <= endLed) {
            CRGB color;

            if (params.colors.size() >= 2) {
                // 多颜色渐变：根据LED在整个分段中的位置进行颜色插值
                float ledRatio = (float)(ledIndex - startLed) / (ledCount - 1);  // 当前LED在整个分段中的比例 (0.0 到 1.0)
                if (ledCount == 1) ledRatio = 0.0;  // 只有一个LED时使用起始颜色

                // 计算在颜色数组中的位置
                float colorPos = ledRatio * (params.colors.size() - 1);
                int colorIndex = (int)colorPos;
                float colorBlend = colorPos - colorIndex;

                // 确保索引在有效范围内
                if (colorIndex >= params.colors.size() - 1) {
                    color = params.colors[params.colors.size() - 1];
                } else {
                    // 在两个颜色之间进行线性插值
                    CRGB color1 = params.colors[colorIndex];
                    CRGB color2 = params.colors[colorIndex + 1];

                    color.r = color1.r + (color2.r - color1.r) * colorBlend;
                    color.g = color1.g + (color2.g - color1.g) * colorBlend;
                    color.b = color1.b + (color2.b - color1.b) * colorBlend;
                }
            } else {
                // 单颜色
                color = params.colors[0];
            }

            leds[ledIndex] = applyBrightness(color, params.brightness);
        }
    }
}

// =================================================================
// 工具方法
// =================================================================

/**
 * 将速度值转换为时间间隔
 */
unsigned long LightEffect::speedToInterval(uint8_t speed) {
    // 速度越快，间隔越短
    // 速度1 = 200ms间隔，速度100 = 10ms间隔
    // 调整后的速度范围更适合彩虹等动态灯效
    return constrainValue(210 - speed * 2, 10, 200);
}

/**
 * 应用亮度到颜色
 */
CRGB LightEffect::applyBrightness(const CRGB& color, uint8_t brightness) {
    return CRGB(
        (color.r * brightness) / 255,
        (color.g * brightness) / 255,
        (color.b * brightness) / 255
    );
}

/**
 * 限制参数值在有效范围内
 */
int LightEffect::constrainValue(int value, int minVal, int maxVal) {
    if (value < minVal) return minVal;
    if (value > maxVal) return maxVal;
    return value;
}
