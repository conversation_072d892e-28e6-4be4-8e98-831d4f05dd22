#include "SegmentsManager.h"
#include "Config.h"

/**
 * 构造函数
 */
SegmentsManager::SegmentsManager(int totalLeds) : totalLeds(totalLeds) {
    Serial.printf("SegmentsManager 初始化，总LED数量: %d\n", totalLeds);
}

/**
 * 析构函数
 */
SegmentsManager::~SegmentsManager() {
    Serial.println("SegmentsManager 析构");
}

/**
 * 初始化分段管理器
 */
void SegmentsManager::begin() {
    Serial.println("初始化 SegmentsManager...");
    
    // 从文件加载配置
    if (!loadFromFile()) {
        Serial.println("未找到分段配置文件，使用空配置");
    }
    
    Serial.printf("SegmentsManager 初始化完成，当前分段数量: %d\n", segments.size());
}

/**
 * 注册API端点到WebServer
 */
void SegmentsManager::registerAPI(AsyncWebServer& server) {
    Serial.println("注册 SegmentsManager API端点...");
    
    // 获取所有分段
    server.on("/api/segments", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSegments(request);
    });
    
    // 获取分段统计信息
    server.on("/api/segments/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetStats(request);
    });
    
    // 创建新分段
    server.on("/api/segments", HTTP_POST, 
        [this](AsyncWebServerRequest* request) {
            // POST请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateSegment(request, data, len, index, total);
        }
    );
    
    // 更新分段 - 使用通配符路由
    server.on("/api/segments/*", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // PUT请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateSegment(request, data, len, index, total);
        }
    );

    // 删除分段 - 使用通配符路由
    server.on("/api/segments/*", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteSegment(request);
    });
    
    Serial.println("SegmentsManager API端点注册完成");
}

/**
 * 获取所有分段
 */
const std::vector<LEDSegment>& SegmentsManager::getSegments() const {
    return segments;
}

/**
 * 根据ID获取分段
 */
const LEDSegment* SegmentsManager::getSegmentById(const String& id) const {
    for (const auto& segment : segments) {
        if (segment.id == id) {
            return &segment;
        }
    }
    return nullptr;
}

/**
 * 添加新分段
 */
bool SegmentsManager::addSegment(const LEDSegment& segment) {
    // 验证分段
    if (!validateSegment(segment)) {
        Serial.printf("分段验证失败: %s\n", segment.name.c_str());
        return false;
    }
    
    // 检查ID是否已存在
    if (getSegmentById(segment.id) != nullptr) {
        Serial.printf("分段ID已存在: %s\n", segment.id.c_str());
        return false;
    }
    
    // 添加分段
    segments.push_back(segment);
    
    // 保存到文件
    if (saveToFile()) {
        Serial.printf("[Segments] 添加成功: %s (ID: %s) - 位置: %d-%d\n",
                     segment.name.c_str(), segment.id.c_str(), segment.startLed, segment.endLed);
        return true;
    } else {
        // 保存失败，回滚
        segments.pop_back();
        Serial.printf("[Segments] 保存失败: %s (ID: %s)\n", segment.name.c_str(), segment.id.c_str());
        return false;
    }
}

/**
 * 更新分段
 */
bool SegmentsManager::updateSegment(const String& id, const LEDSegment& segment) {
    // 验证分段（排除当前分段）
    if (!validateSegment(segment, id)) {
        Serial.printf("分段验证失败: %s\n", segment.name.c_str());
        return false;
    }
    
    // 查找并更新分段
    for (auto& seg : segments) {
        if (seg.id == id) {
            LEDSegment oldSegment = seg;  // 备份原数据
            seg = segment;
            seg.id = id;  // 保持ID不变
            
            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Segments] 更新成功: %s (ID: %s) - 位置: %d-%d\n",
                             segment.name.c_str(), id.c_str(), segment.startLed, segment.endLed);
                return true;
            } else {
                // 保存失败，回滚
                seg = oldSegment;
                Serial.printf("[Segments] 保存失败: %s (ID: %s)\n", segment.name.c_str(), id.c_str());
                return false;
            }
        }
    }
    
    Serial.printf("未找到分段: %s\n", id.c_str());
    return false;
}

/**
 * 删除分段
 */
bool SegmentsManager::deleteSegment(const String& id) {
    for (auto it = segments.begin(); it != segments.end(); ++it) {
        if (it->id == id) {
            LEDSegment deletedSegment = *it;  // 备份数据
            segments.erase(it);
            
            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Segments] 删除成功: %s (ID: %s)\n",
                             deletedSegment.name.c_str(), deletedSegment.id.c_str());
                return true;
            } else {
                // 保存失败，回滚
                segments.insert(it, deletedSegment);
                Serial.printf("[Segments] 保存失败: %s (ID: %s)\n",
                             deletedSegment.name.c_str(), deletedSegment.id.c_str());
                return false;
            }
        }
    }
    
    Serial.printf("未找到分段: %s\n", id.c_str());
    return false;
}

/**
 * 获取总LED数量
 */
int SegmentsManager::getTotalLeds() const {
    return totalLeds;
}

/**
 * 获取已使用的LED数量
 */
int SegmentsManager::getUsedLeds() const {
    int used = 0;
    for (const auto& segment : segments) {
        used += segment.getLength();
    }
    return used;
}

/**
 * 获取剩余可用的LED数量
 */
int SegmentsManager::getAvailableLeds() const {
    return totalLeds - getUsedLeds();
}

/**
 * 获取下一个分段的建议起始位置
 */
int SegmentsManager::getNextStartPosition() const {
    if (segments.empty()) {
        return 1;  // 第一个分段从1开始
    }
    
    // 找到最大的结束位置
    int maxEnd = 0;
    for (const auto& segment : segments) {
        if (segment.endLed > maxEnd) {
            maxEnd = segment.endLed;
        }
    }
    
    return maxEnd + 1;
}

/**
 * 验证分段是否有效
 */
bool SegmentsManager::validateSegment(const LEDSegment& segment, const String& excludeId) const {
    // 检查范围是否有效
    if (segment.startLed < 1 || segment.endLed > totalLeds || segment.startLed > segment.endLed) {
        Serial.printf("分段范围无效 (错误码: %d): %d-%d (总数: %d)\n",
                     ERROR_VALIDATION_FAILED, segment.startLed, segment.endLed, totalLeds);
        return false;
    }

    // 检查名称是否为空
    if (segment.name.isEmpty()) {
        Serial.printf("分段名称不能为空 (错误码: %d)\n", ERROR_VALIDATION_FAILED);
        return false;
    }
    
    // 检查是否与其他分段重叠
    for (const auto& existingSegment : segments) {
        if (existingSegment.id == excludeId) {
            continue;  // 跳过被排除的分段
        }
        
        if (segment.overlapsWith(existingSegment)) {
            Serial.printf("分段重叠 (错误码: %d): %s (%d-%d) 与 %s (%d-%d)\n",
                ERROR_VALIDATION_FAILED, segment.name.c_str(), segment.startLed, segment.endLed,
                existingSegment.name.c_str(), existingSegment.startLed, existingSegment.endLed);
            return false;
        }
    }
    
    return true;
}

/**
 * 生成唯一的分段ID
 */
String SegmentsManager::generateSegmentId() const {
    return generateUniqueId("segments");
}

/**
 * 保存分段配置到文件
 */
bool SegmentsManager::saveToFile() {
    StaticJsonDocument<JSON_SEGMENTS_BUFFER_SIZE> doc;
    JsonArray segmentsArray = doc.createNestedArray("segments");

    // 使用统一的序列化方法
    for (const auto& segment : segments) {
        JsonObject segObj = segmentsArray.createNestedObject();
        segObj["id"] = segment.id;
        segObj["name"] = segment.name;
        segObj["startLed"] = segment.startLed;
        segObj["endLed"] = segment.endLed;
    }

    doc["totalLeds"] = totalLeds;
    doc["version"] = "1.0";

    File file = LittleFS.open(configFile, "w");
    if (!file) {
        Serial.printf("文件写入失败 (错误码: %d): %s\n", ERROR_FILE_IO, configFile.c_str());
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    if (bytesWritten > 0) {
        Serial.printf("[Segments] 配置已保存: %s (%d bytes)\n", configFile.c_str(), bytesWritten);
        return true;
    } else {
        Serial.printf("[Segments] 保存配置失败 (错误码: %d): %s\n", ERROR_FILE_IO, configFile.c_str());
        return false;
    }
}

/**
 * 从文件加载分段配置
 */
bool SegmentsManager::loadFromFile() {
    if (!LittleFS.exists(configFile)) {
        Serial.printf("配置文件不存在: %s\n", configFile.c_str());
        return false;
    }

    File file = LittleFS.open(configFile, "r");
    if (!file) {
        Serial.printf("无法打开文件进行读取: %s\n", configFile.c_str());
        return false;
    }

    StaticJsonDocument<2048> doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("解析配置文件失败: %s\n", error.c_str());
        return false;
    }

    // 清空现有分段
    segments.clear();

    // 加载分段数据
    JsonArray segmentsArray = doc["segments"];
    for (JsonObject segObj : segmentsArray) {
        LEDSegment segment;
        segment.id = segObj["id"].as<String>();
        segment.name = segObj["name"].as<String>();
        segment.startLed = segObj["startLed"].as<int>();
        segment.endLed = segObj["endLed"].as<int>();

        // 验证分段数据
        if (validateSegment(segment)) {
            segments.push_back(segment);
            Serial.printf("加载分段: %s (%d-%d)\n", segment.name.c_str(), segment.startLed, segment.endLed);
        } else {
            Serial.printf("跳过无效分段: %s (%d-%d)\n", segment.name.c_str(), segment.startLed, segment.endLed);
        }
    }

    Serial.printf("分段配置加载完成，共加载 %d 个分段\n", segments.size());
    return true;
}

/**
 * 处理获取所有分段的请求
 */
void SegmentsManager::handleGetSegments(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_SEGMENTS_BUFFER_SIZE> doc;
    JsonArray segmentsArray = doc.createNestedArray("segments");

    // 使用统一的序列化方法
    for (const auto& segment : segments) {
        JsonObject segObj = segmentsArray.createNestedObject();
        segObj["id"] = segment.id;
        segObj["name"] = segment.name;
        segObj["startLed"] = segment.startLed;
        segObj["endLed"] = segment.endLed;
        segObj["length"] = segment.getLength();
    }

    JsonObject stats = doc.createNestedObject("stats");
    stats["totalLeds"] = totalLeds;
    stats["usedLeds"] = getUsedLeds();
    stats["availableLeds"] = getAvailableLeds();
    stats["nextStartPosition"] = getNextStartPosition();

    sendJsonResponse(request, 200, "获取分段列表成功", &doc);
}

/**
 * 处理获取统计信息的请求
 */
void SegmentsManager::handleGetStats(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_STATUS_BUFFER_SIZE> doc;
    JsonObject stats = doc.createNestedObject("stats");
    stats["totalLeds"] = totalLeds;
    stats["usedLeds"] = getUsedLeds();
    stats["availableLeds"] = getAvailableLeds();
    stats["nextStartPosition"] = getNextStartPosition();
    stats["segmentCount"] = segments.size();

    sendJsonResponse(request, 200, "获取统计信息成功", &doc);
}

/**
 * 处理创建分段的请求
 */
void SegmentsManager::handleCreateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {  // 确保接收完整数据
        String body = String((char*)data, len);

        StaticJsonDocument<512> doc;
        DeserializationError error = deserializeJson(doc, body);

        if (error) {
            sendErrorResponse(request, 400, "JSON格式无效");
            return;
        }

        // 创建分段对象
        LEDSegment segment;
        segment.id = generateSegmentId();
        segment.name = doc["name"].as<String>();
        segment.startLed = doc["startLed"].as<int>();
        segment.endLed = doc["endLed"].as<int>();

        // 添加分段
        if (addSegment(segment)) {
            StaticJsonDocument<256> responseDoc;
            JsonObject segObj = responseDoc.createNestedObject("segment");
            segObj["id"] = segment.id;
            segObj["name"] = segment.name;
            segObj["startLed"] = segment.startLed;
            segObj["endLed"] = segment.endLed;
            segObj["length"] = segment.getLength();

            sendJsonResponse(request, HTTP_OK, "分段创建成功", &responseDoc);
        } else {
            sendErrorResponse(request, 400, "分段创建失败");
        }
    }
}

/**
 * 处理更新分段的请求
 */
void SegmentsManager::handleUpdateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {  // 确保接收完整数据
        // 从URL中提取分段ID
        String url = request->url();
        String segmentId = url.substring(url.lastIndexOf('/') + 1);
        String body = String((char*)data, len);

        StaticJsonDocument<512> doc;
        DeserializationError error = deserializeJson(doc, body);

        if (error) {
            sendErrorResponse(request, 400, "JSON格式无效");
            return;
        }

        // 创建分段对象
        LEDSegment segment;
        segment.name = doc["name"].as<String>();
        segment.startLed = doc["startLed"].as<int>();
        segment.endLed = doc["endLed"].as<int>();

        // 更新分段
        if (updateSegment(segmentId, segment)) {
            StaticJsonDocument<256> responseDoc;
            JsonObject segObj = responseDoc.createNestedObject("segment");
            segObj["id"] = segmentId;
            segObj["name"] = segment.name;
            segObj["startLed"] = segment.startLed;
            segObj["endLed"] = segment.endLed;
            segObj["length"] = segment.getLength();

            sendJsonResponse(request, 200, "分段更新成功", &responseDoc);
        } else {
            sendErrorResponse(request, 400, "分段更新失败");
        }
    }
}

/**
 * 处理删除分段的请求
 */
void SegmentsManager::handleDeleteSegment(AsyncWebServerRequest* request) {
    // 从URL中提取分段ID
    String url = request->url();
    String segmentId = url.substring(url.lastIndexOf('/') + 1);

    if (deleteSegment(segmentId)) {
        StaticJsonDocument<128> responseDoc;
        responseDoc["deletedId"] = segmentId;
        sendJsonResponse(request, 200, "分段删除成功", &responseDoc);
    } else {
        sendErrorResponse(request, 404, "分段不存在");
    }
}

/**
 * 发送JSON响应
 */
void SegmentsManager::sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
    responseDoc["success"] = (code == HTTP_OK);
    responseDoc["message"] = message;
    responseDoc["timestamp"] = millis();

    if (data != nullptr && !data->isNull()) {
        responseDoc["data"] = *data;
    }

    String response;
    serializeJson(responseDoc, response);

    request->send(code, "application/json", response);
}

/**
 * 发送错误响应
 */
void SegmentsManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    sendJsonResponse(request, code, message, nullptr);
}
