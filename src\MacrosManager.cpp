#include "MacrosManager.h"
#include "MoonrakerClient.h"
#include "PrinterManager.h"
#include "ScenesManager.h"

/**
 * 构造函数
 */
MacrosManager::MacrosManager(MoonrakerClient& moonrakerClient) : moonraker(moonrakerClient), printer<PERSON>ana<PERSON>(nullptr), scenesManager(nullptr) {
    // 初始化成员变量
}

/**
 * 析构函数
 */
MacrosManager::~MacrosManager() {
    // 清理资源
}

/**
 * 初始化宏管理器
 */
void MacrosManager::begin() {
    Serial.println("初始化 MacrosManager...");
    
    // 从文件加载宏配置
    if (!loadFromFile()) {
        Serial.println("未找到宏配置文件，使用默认配置");
    }
    
    Serial.printf("MacrosManager 初始化完成，加载了 %d 个宏配置\n", macros.size());
}

/**
 * 主循环处理函数
 */
void MacrosManager::loop() {
    // 更新宏状态
    updateMacroStates();
    
    // 检查状态变化并广播
    checkAndBroadcastChanges();
}

/**
 * 注册API端点到WebServer
 */
void MacrosManager::registerAPI(AsyncWebServer& server) {
    // 获取所有宏配置
    server.on("/api/macros", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetMacros(request);
    });
    
    // 创建新宏配置
    server.on("/api/macros", HTTP_POST, 
        [this](AsyncWebServerRequest* request) {
            // POST请求的响应在onBody中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateMacro(request, data, len, index, total);
        }
    );
    
    // 更新宏配置
    server.on("/api/macros/*", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // PUT请求的响应在onBody中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateMacro(request, data, len, index, total);
        }
    );
    
    // 删除宏配置
    server.on("/api/macros/*", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteMacro(request);
    });
    
    // 获取宏统计信息
    server.on("/api/macros/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetStats(request);
    });
    
    Serial.println("MacrosManager API端点已注册");
}

/**
 * 获取所有宏配置
 */
const std::vector<MacroConfig>& MacrosManager::getMacros() const {
    return macros;
}

/**
 * 根据ID获取宏配置
 */
const MacroConfig* MacrosManager::getMacroById(const String& id) const {
    for (const auto& macro : macros) {
        if (macro.id == id) {
            return &macro;
        }
    }
    return nullptr;
}

/**
 * 添加新宏配置
 */
bool MacrosManager::addMacro(const MacroConfig& macro) {
    // 验证宏配置
    if (!validateMacro(macro)) {
        return false;
    }
    
    // 添加到列表
    macros.push_back(macro);

    // 保存到文件
    if (saveToFile()) {
        Serial.printf("[Macros] 添加成功: %s (ID: %s) - 变量: %s\n",
                     macro.displayName.c_str(), macro.id.c_str(), macro.variableName.c_str());
        return true;
    } else {
        // 保存失败，回滚
        macros.pop_back();
        Serial.printf("[Macros] 保存失败: %s (ID: %s)\n", macro.displayName.c_str(), macro.id.c_str());
        return false;
    }
}

/**
 * 更新宏配置
 */
bool MacrosManager::updateMacro(const String& id, const MacroConfig& macro) {
    // 验证宏配置
    if (!validateMacro(macro, id)) {
        return false;
    }
    
    // 查找并更新
    for (auto& m : macros) {
        if (m.id == id) {
            // 保留当前状态和时间戳
            MacroStatus currentStatus = m.currentStatus;
            unsigned long lastUpdate = m.lastUpdateTime;
            
            m = macro;
            m.id = id; // 确保ID不变
            m.currentStatus = currentStatus;
            m.lastUpdateTime = lastUpdate;

            if (saveToFile()) {
                Serial.printf("[Macros] 更新成功: %s (ID: %s) - 变量: %s\n",
                             macro.displayName.c_str(), id.c_str(), macro.variableName.c_str());
                return true;
            } else {
                Serial.printf("[Macros] 保存失败: %s (ID: %s)\n", macro.displayName.c_str(), id.c_str());
                return false;
            }
        }
    }
    
    return false;
}

/**
 * 删除宏配置
 */
bool MacrosManager::deleteMacro(const String& id) {
    auto it = std::remove_if(macros.begin(), macros.end(),
        [&id](const MacroConfig& macro) {
            return macro.id == id;
        });
    
    if (it != macros.end()) {
        // 备份要删除的宏信息
        String deletedName = it->displayName;
        String deletedId = it->id;

        macros.erase(it, macros.end());

        if (saveToFile()) {
            Serial.printf("[Macros] 删除成功: %s (ID: %s)\n", deletedName.c_str(), deletedId.c_str());
            return true;
        } else {
            Serial.printf("[Macros] 保存失败: %s (ID: %s)\n", deletedName.c_str(), deletedId.c_str());
            return false;
        }
    }

    return false;
}

/**
 * 获取宏统计信息
 */
MacrosManager::MacroStats MacrosManager::getStats() const {
    MacroStats stats = {0, 0, 0};
    
    for (const auto& macro : macros) {
        stats.totalMacros++;
        switch (macro.currentStatus) {
            case MacroStatus::RUNNING:
                stats.runningMacros++;
                break;
            case MacroStatus::IDLE:
                stats.idleMacros++;
                break;
        }
    }
    
    return stats;
}

/**
 * 生成唯一的宏ID
 */
String MacrosManager::generateMacroId() const {
    return generateUniqueId("macros");
}

/**
 * 验证宏配置是否有效
 */
bool MacrosManager::validateMacro(const MacroConfig& macro, const String& excludeId) const {
    // 检查显示名称
    if (macro.displayName.isEmpty()) {
        return false;
    }
    
    // 检查变量名称
    if (macro.variableName.isEmpty()) {
        return false;
    }
    
    // 检查是否有重复的变量名称
    for (const auto& m : macros) {
        if (m.id != excludeId && m.variableName == macro.variableName) {
            return false;
        }
    }
    
    return true;
}

/**
 * 宏状态变化回调函数
 */
void MacrosManager::onMacroStatusChanged() {
    // 立即更新宏状态
    updateMacroStates();

    // 通知PrinterManager推送状态变化
    notifyPrinterManager();
}

/**
 * 设置PrinterManager引用
 */
void MacrosManager::setPrinterManager(PrinterManager* printerMgr) {
    printerManager = printerMgr;
}

/**
 * 设置ScenesManager引用
 */
void MacrosManager::setScenesManager(ScenesManager* scenesMgr) {
    scenesManager = scenesMgr;
}

/**
 * 保存宏配置到文件
 */
bool MacrosManager::saveToFile() {
    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    JsonArray array = doc.createNestedArray("macros");
    
    for (const auto& macro : macros) {
        JsonObject obj = array.createNestedObject();
        macro.toJson(obj);
    }
    
    File file = LittleFS.open(configFile, "w");
    if (!file) {
        Serial.println("无法打开宏配置文件进行写入");
        return false;
    }
    
    size_t bytesWritten = serializeJson(doc, file);
    file.close();
    
    if (bytesWritten == 0) {
        Serial.println("写入宏配置文件失败");
        return false;
    }
    
    Serial.printf("[Macros] 配置已保存: %s (%d bytes)\n", configFile.c_str(), bytesWritten);
    return true;
}

/**
 * 从文件加载宏配置
 */
bool MacrosManager::loadFromFile() {
    if (!LittleFS.exists(configFile)) {
        return false;
    }
    
    File file = LittleFS.open(configFile, "r");
    if (!file) {
        Serial.println("无法打开宏配置文件进行读取");
        return false;
    }
    
    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();
    
    if (error) {
        Serial.printf("解析宏配置文件失败: %s\n", error.c_str());
        return false;
    }
    
    macros.clear();
    
    if (doc.containsKey("macros")) {
        JsonArray array = doc["macros"];
        for (JsonObject obj : array) {
            MacroConfig macro = MacroConfig::fromJson(obj);
            macros.push_back(macro);
        }
    }
    
    Serial.printf("从文件加载了 %d 个宏配置\n", macros.size());
    return true;
}

/**
 * 更新宏状态
 */
void MacrosManager::updateMacroStates() {
    bool hasChanges = false;

    for (auto& macro : macros) {
        if (!macro.enabled) {
            continue;
        }

        // 从MoonrakerClient获取宏变量值
        String variableValue = moonraker.getMacroVariable(macro.variableName);

        // 确定新状态
        MacroStatus newStatus = MacroStatus::IDLE;
        if (variableValue == "running") {
            newStatus = MacroStatus::RUNNING;
        }

        // 检查状态是否变化
        if (macro.currentStatus != newStatus) {
            macro.currentStatus = newStatus;
            macro.lastUpdateTime = millis();
            hasChanges = true;

#if ENABLE_SERIAL_STATUS_OUTPUT
            Serial.printf("宏状态变化: %s -> %s\n",
                         macro.displayName.c_str(),
                         MacroConfig::statusToString(newStatus).c_str());
#endif
        }
    }

    // 如果有变化，保存到文件
    if (hasChanges) {
        saveToFile();
    }
}

/**
 * 检查状态是否有变化并推送更新
 */
void MacrosManager::checkAndBroadcastChanges() {
    bool hasChanges = false;

    // 检查是否有状态变化，并通知ScenesManager具体的变化
    if (lastMacroStates.size() != macros.size()) {
        hasChanges = true;
    } else {
        for (size_t i = 0; i < macros.size(); i++) {
            if (i >= lastMacroStates.size() ||
                macros[i].currentStatus != lastMacroStates[i].currentStatus ||
                macros[i].lastUpdateTime != lastMacroStates[i].lastUpdateTime) {

                // 通知ScenesManager宏状态变化
                if (scenesManager && i < lastMacroStates.size()) {
                    scenesManager->onMacroStatusChanged(
                        macros[i].variableName,
                        static_cast<int>(macros[i].currentStatus),
                        static_cast<int>(lastMacroStates[i].currentStatus)
                    );
                }

                hasChanges = true;
            }
        }
    }

    if (hasChanges) {
        notifyPrinterManager();
        lastMacroStates = macros; // 更新缓存
    }
}

/**
 * 通知PrinterManager推送宏状态更新
 */
void MacrosManager::notifyPrinterManager() {
    if (printerManager != nullptr) {
        // 通知PrinterManager有宏状态变化，需要推送更新
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.println("通知PrinterManager推送宏状态更新");
#endif
        printerManager->onMacroStatusChanged();
    }
}

/**
 * 处理获取所有宏配置请求
 */
void MacrosManager::handleGetMacros(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    JsonArray array = doc.createNestedArray("macros");

    for (const auto& macro : macros) {
        JsonObject obj = array.createNestedObject();
        macro.toJson(obj);
    }

    sendJsonResponse(request, HTTP_OK, "success", &doc);
}

/**
 * 处理创建宏配置请求
 */
void MacrosManager::handleCreateMacro(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len != total) {
        return; // 等待所有数据接收完成
    }

    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, data, len);

    if (error) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败");
        return;
    }

    // 创建宏配置
    MacroConfig macro;
    macro.id = generateMacroId();
    macro.displayName = doc["displayName"].as<String>();
    macro.variableName = doc["variableName"].as<String>();
    macro.enabled = doc.containsKey("enabled") ? doc["enabled"].as<bool>() : true;
    macro.currentStatus = MacroStatus::IDLE;
    macro.lastUpdateTime = millis();

    if (addMacro(macro)) {
        StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> responseDoc;
        JsonObject obj = responseDoc.to<JsonObject>();
        macro.toJson(obj);
        sendJsonResponse(request, HTTP_OK, "宏配置创建成功", &responseDoc);
    } else {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "宏配置创建失败");
    }
}

/**
 * 处理更新宏配置请求
 */
void MacrosManager::handleUpdateMacro(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len != total) {
        return; // 等待所有数据接收完成
    }

    // 从URL中提取宏ID
    String path = request->url();
    int lastSlash = path.lastIndexOf('/');
    if (lastSlash == -1) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "无效的URL");
        return;
    }
    String macroId = path.substring(lastSlash + 1);

    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, data, len);

    if (error) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败");
        return;
    }

    // 创建更新的宏配置
    MacroConfig macro;
    macro.displayName = doc["displayName"].as<String>();
    macro.variableName = doc["variableName"].as<String>();
    macro.enabled = doc.containsKey("enabled") ? doc["enabled"].as<bool>() : true;

    if (updateMacro(macroId, macro)) {
        const MacroConfig* updatedMacro = getMacroById(macroId);
        if (updatedMacro) {
            StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> responseDoc;
            JsonObject obj = responseDoc.to<JsonObject>();
            updatedMacro->toJson(obj);
            sendJsonResponse(request, HTTP_OK, "宏配置更新成功", &responseDoc);
        } else {
            sendJsonResponse(request, HTTP_OK, "宏配置更新成功");
        }
    } else {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "宏配置更新失败");
    }
}

/**
 * 处理删除宏配置请求
 */
void MacrosManager::handleDeleteMacro(AsyncWebServerRequest* request) {
    // 从URL中提取宏ID
    String path = request->url();
    int lastSlash = path.lastIndexOf('/');
    if (lastSlash == -1) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "无效的URL");
        return;
    }
    String macroId = path.substring(lastSlash + 1);

    if (deleteMacro(macroId)) {
        sendJsonResponse(request, HTTP_OK, "宏配置删除成功");
    } else {
        sendErrorResponse(request, HTTP_NOT_FOUND, "宏配置不存在");
    }
}

/**
 * 处理获取宏统计信息请求
 */
void MacrosManager::handleGetStats(AsyncWebServerRequest* request) {
    MacroStats stats = getStats();

    StaticJsonDocument<JSON_MACROS_BUFFER_SIZE> doc;
    doc["totalMacros"] = stats.totalMacros;
    doc["runningMacros"] = stats.runningMacros;
    doc["idleMacros"] = stats.idleMacros;

    sendJsonResponse(request, HTTP_OK, "success", &doc);
}

/**
 * 发送JSON响应
 */
void MacrosManager::sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
    responseDoc["success"] = (code == HTTP_OK);
    responseDoc["message"] = message;
    responseDoc["timestamp"] = millis();

    if (data != nullptr && !data->isNull()) {
        responseDoc["data"] = *data;
    }

    String response;
    serializeJson(responseDoc, response);
    request->send(code, "application/json", response);
}

/**
 * 发送错误响应
 */
void MacrosManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    sendJsonResponse(request, code, message, nullptr);
}
