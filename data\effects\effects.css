/* 灯效预设模块样式 */

/* 卡片头部控件布局 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 预览开关样式 */
.preview-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
    user-select: none;
}

.preview-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 40px;
    height: 20px;
    background-color: #ccc;
    border-radius: 20px;
    position: relative;
    transition: background-color 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.preview-toggle input:checked + .toggle-slider {
    background-color: #007bff;
}

.preview-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.preview-toggle:hover .toggle-slider {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.toggle-label {
    font-weight: 500;
    transition: color 0.3s ease;
}

.preview-toggle input:checked ~ .toggle-label {
    color: #007bff;
}

/* 预览模式状态指示器 */
.preview-mode-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    z-index: 1000;
    animation: pulse 2s infinite;
    display: none;
}

.preview-mode-indicator.active {
    display: block;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(255, 107, 107, 0.5);
    }
    100% {
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    }
}

/* 灯效模块卡片头部布局 */
#effects-module .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 添加按钮 */
.add-effect-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    flex-shrink: 0;
}

.add-effect-btn:hover {
    background: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* 灯效模块模态框样式 */
.module-modal-effects {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.module-modal-effects.show {
    opacity: 1;
    visibility: visible;
}

.module-modal-effects-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.2s ease;
    will-change: transform;
}

.module-modal-effects.show .module-modal-effects-content {
    transform: scale(1);
}

.module-modal-effects-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
}

.module-modal-effects-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.module-modal-effects-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.module-modal-effects-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.module-modal-effects-body {
    padding: 20px 24px;
}

/* 灯效列表样式 */
.effects-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.effect-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    background: white;
    transition: all 0.2s ease;
}

.effect-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.effect-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.effect-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.effect-icon {
    font-size: 1rem;
}

.effect-name {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.effect-actions {
    display: flex;
    gap: 8px;
}

.effect-edit-btn,
.effect-delete-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.effect-edit-btn {
    background: #ffc107;
    color: #212529;
}

.effect-edit-btn:hover {
    background: #e0a800;
}

.effect-delete-btn {
    background: #dc3545;
    color: white;
}

.effect-delete-btn:hover {
    background: #c82333;
}

.effect-details {
    display: flex;
    flex-direction: column;
    gap: 3px;
    font-size: 0.75rem;
    color: #6c757d;
}

.effect-type,
.effect-segments,
.effect-params {
    display: flex;
    align-items: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.6;
}

.empty-state-text {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state-hint {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 表单样式 */
.module-modal-effects .effects-form-group {
    margin-bottom: 20px;
}

.module-modal-effects .effects-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

/* 标题行样式 */
.module-modal-effects .effects-form-label-row {
    display: flex;
    align-items: baseline; /* 改为baseline对齐，确保文字基线对齐 */
    margin-bottom: 8px;
    gap: 12px;
}

/* 灯效类型描述样式 */
.module-modal-effects .effect-type-description {
    font-size: 0.8rem; /* 稍微增大字体，与标题更匹配 */
    color: #6c757d;
    font-style: normal;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    max-width: 370px; /* 稍微增加最大宽度 */
    line-height: 1; /* 确保行高一致 */
}

.module-modal-effects .effect-type-description.show {
    opacity: 1;
    visibility: visible;
}

.module-modal-effects .effects-form-label span {
    color: #dc3545;
}

.module-modal-effects .effects-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
}

.module-modal-effects .effects-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.module-modal-effects .effects-form-control:disabled {
    background-color: #e9ecef;
    color: #6c757d;
}

.module-modal-effects .effects-form-control.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.module-modal-effects .effects-form-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

/* 紧凑型参数布局 */
.module-modal-effects .effects-form-group.compact {
    margin-bottom: 12px;
}

.module-modal-effects .param-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.module-modal-effects .param-row .effects-form-label {
    min-width: 60px;
    margin-bottom: 0;
    flex-shrink: 0;
}

.module-modal-effects .param-slider {
    flex: 1;
    margin: 0;
}

.module-modal-effects .param-value {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.module-modal-effects .color-picker {
    width: 40px;
    height: 32px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    cursor: pointer;
    padding: 0;
}

/* 分段按钮样式 */
.module-modal-effects .segments-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.module-modal-effects .segment-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #d7d6d6;
    border-radius: 4px;
    background: #d7d6d6;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    color: #6c757d;
}

.module-modal-effects .segment-button:hover {
    border-color: #007bff;
    background: #e9ecef;
}

.module-modal-effects .segment-button.selected {
    border-color: #007bff;
    background: #e3f2fd;
    color: #007bff;
}

.module-modal-effects .segment-name {
    font-weight: 500;
    white-space: nowrap;
}

.module-modal-effects .segment-direction-buttons {
    display: none;
    gap: 2px;
    margin-left: 2px;
}

.module-modal-effects .direction-btn {
    padding: 2px 6px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    background: white;
    color: #6c757d;
    font-size: 0.65rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 20px;
    text-align: center;
    font-weight: 500;
}

.module-modal-effects .direction-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

.module-modal-effects .direction-btn.active {
    border-color: #007bff;
    background: #007bff;
    color: white;
    font-weight: 600;
}

.module-modal-effects .effects-form-hint {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 4px;
}

/* 分段选择区域 */
.module-modal-effects .segments-selection {
    padding: 0;
    background: transparent;
}





.module-modal-effects .param-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.module-modal-effects .param-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    transition: all 0.2s ease;
}

.module-modal-effects .param-slider::-webkit-slider-thumb:hover {
    background: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.module-modal-effects .param-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}





.module-modal-effects .color-picker {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: none;
    transition: border-color 0.2s ease;
}

.module-modal-effects .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
}

.module-modal-effects .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
}

.module-modal-effects .color-picker:hover {
    border-color: #007bff;
}

/* 拖拽排序式颜色选择器样式 */
.module-modal-effects .multi-color-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.module-modal-effects .color-list {
    display: flex;
    flex-direction: row;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
    min-height: 60px;
    padding: 8px;
    border-radius: 8px;
    background: #f8f9fa;
}

.module-modal-effects .color-item {
    position: relative;
    width: 60px;
    height: 35px;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.module-modal-effects .color-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.module-modal-effects .color-item.dragging {
    cursor: grabbing;
    opacity: 0.8;
    transform: rotate(5deg) scale(1.05);
    z-index: 1000;
}

.module-modal-effects .color-item .color-picker {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
    cursor: inherit;
    padding: 0;
    appearance: none;
    background: none;
}

.module-modal-effects .color-item .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 6px;
}

.module-modal-effects .color-item .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 6px;
}

/* 删除按钮 */
.module-modal-effects .color-item .delete-btn {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
}

.module-modal-effects .color-item:hover .delete-btn {
    opacity: 1;
}

.module-modal-effects .color-item .delete-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* 添加颜色按钮 */
.module-modal-effects .add-color-item {
    width: 60px;
    height: 35px;
    border: 2px dashed #007bff;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #007bff;
    font-size: 20px;
    font-weight: bold;
}

.module-modal-effects .add-color-item:hover {
    background: #007bff;
    color: white;
    border-style: solid;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

/* 拖拽占位符 */
.module-modal-effects .drag-placeholder {
    width: 60px;
    height: 35px;
    border: 2px dashed #6c757d;
    border-radius: 6px;
    background: rgba(108, 117, 125, 0.1);
    opacity: 0.5;
}

/* 隐藏原来的按钮 */
.module-modal-effects .color-item .color-actions,
.module-modal-effects .color-item .remove-color-btn {
    display: none;
}

.module-modal-effects .add-color-btn {
    padding: 12px 20px;
    border: 2px dashed #007bff;
    border-radius: 25px;
    background: transparent;
    color: #007bff;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    align-self: flex-start;
}

.module-modal-effects .add-color-btn:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-style: solid;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.module-modal-effects .add-color-btn:disabled {
    color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.module-modal-effects .color-count-hint {
    font-size: 0.8rem;
    color: #6c757d;
    align-self: flex-start;
}



/* 模态框底部 */
.module-modal-effects-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    min-height: 60px;
}

/* 按钮样式 */
.module-modal-effects .effects-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 80px;
}

.module-modal-effects .effects-btn-primary {
    background: #007bff;
    color: white;
}

.module-modal-effects .effects-btn-primary:hover {
    background: #0056b3;
}

.module-modal-effects .effects-btn-secondary {
    background: #6c757d;
    color: white;
}

.module-modal-effects .effects-btn-secondary:hover {
    background: #545b62;
}

.module-modal-effects .effects-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-modal-effects-content {
        width: 95%;
        margin: 20px;
    }

    .module-modal-effects-header {
        padding: 15px 20px;
    }

    .module-modal-effects-body {
        padding: 15px 20px;
    }

    .module-modal-effects-footer {
        padding: 15px 20px;
        gap: 10px;
    }

    .module-modal-effects .effects-btn {
        min-width: 70px;
        padding: 8px 12px;
    }

    /* 温度映射响应式 */
    .temperature-mapping-container {
        flex-direction: column;
    }
}

/* 温度映射专用样式 */
.temperature-mapping-container {
    display: flex;
    gap: 16px;
    margin-top: 16px;
}

.temp-source-section {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    background: #f8f9fa;
    transition: border-color 0.3s ease;
}

.temp-source-section.disabled {
    opacity: 0.6;
    background: #f1f3f4;
}

.temp-source-section.disabled .temp-source-params {
    pointer-events: none;
}

.temp-source-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #dee2e6;
}

.temp-source-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    user-select: none;
}

.temp-source-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #007bff;
    cursor: pointer;
}

.temp-source-icon {
    font-size: 1.2rem;
}

.temp-source-label {
    font-size: 1rem;
}

.temp-source-params {
    display: block;
}

.temp-source-params.hidden {
    display: none;
}

/* 温度映射参数样式复用现有样式 */
.temp-source-section .effects-form-group {
    margin-bottom: 16px;
}

.temp-source-section .param-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.temp-source-section .param-label {
    min-width: 60px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.temp-source-section .param-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    cursor: pointer;
}

.temp-source-section .param-value {
    min-width: 40px;
    text-align: right;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

/* 温度源分段选择样式 */
.temp-segment-button {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin: 4px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    min-width: 80px;
}

.temp-segment-button:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.temp-segment-button.selected {
    border-color: #007bff;
    background: #e3f2fd;
    color: #0056b3;
}

.temp-segment-button .segment-name {
    font-weight: 500;
}

.temp-segment-button .segment-direction-buttons {
    display: none;
    gap: 4px;
    margin-left: 8px;
}

.temp-segment-button.selected .segment-direction-buttons {
    display: flex;
}

.temp-segment-button .direction-btn {
    padding: 2px 6px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.temp-segment-button .direction-btn:hover {
    background: #f0f0f0;
}

.temp-segment-button .direction-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.no-segments {
    color: #6c757d;
    font-style: italic;
    padding: 12px;
    text-align: center;
    background: #f8f9fa;
    border-radius: 4px;
}
