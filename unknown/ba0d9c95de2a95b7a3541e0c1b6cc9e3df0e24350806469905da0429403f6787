<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Moonraker 监控</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="printer/printer.css">
    <link rel="stylesheet" href="segments/segments.css">
    <link rel="stylesheet" href="effects/effects.css">
    <link rel="stylesheet" href="scenes/scenes.css">
    <link rel="stylesheet" href="macros/macros.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>ESP32 Moonraker 监控</h1>
            <div class="connection-status">
                <span id="connection-indicator" class="status-indicator disconnected">●</span>
                <span id="connection-text">连接中...</span>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 打印机状态区域 - 上方两个卡片 -->
            <section class="printer-status-section">
                <!-- 左侧卡片：打印机状态和进度 -->
                <div class="status-card">
                    <div class="card-header">
                        <h2>打印机状态</h2>
                        <span id="printer-state" class="status-badge">--</span>
                    </div>
                    <div class="card-content">
                        <div class="status-item">
                            <label>文件:</label>
                            <span id="print-filename" class="status-value">--</span>
                        </div>
                        <div class="status-item">
                            <label>进度:</label>
                            <span id="print-progress" class="status-value">--</span>
                        </div>
                        <!-- 打印进度条 -->
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div id="print-progress-bar" class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧卡片：温度信息 -->
                <div class="temperature-card">
                    <div class="card-header">
                        <h2>温度监控</h2>
                    </div>
                    <div class="card-content">
                        <!-- 喷嘴温度 -->
                        <div class="temp-item">
                            <div class="temp-header">
                                <span class="temp-label">喷嘴</span>
                                <div class="temp-display">
                                    <span id="extruder-temp" class="temp-current">--</span>
                                    <span class="temp-separator">/</span>
                                    <span id="extruder-target" class="temp-target">--</span>
                                    <span class="temp-unit">°C</span>
                                </div>
                            </div>
                            <div class="temp-bar">
                                <div id="extruder-progress" class="temp-progress"></div>
                            </div>
                        </div>

                        <!-- 热床温度 -->
                        <div class="temp-item">
                            <div class="temp-header">
                                <span class="temp-label">热床</span>
                                <div class="temp-display">
                                    <span id="bed-temp" class="temp-current">--</span>
                                    <span class="temp-separator">/</span>
                                    <span id="bed-target" class="temp-target">--</span>
                                    <span class="temp-unit">°C</span>
                                </div>
                            </div>
                            <div class="temp-bar">
                                <div id="bed-progress" class="temp-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 宏状态监控区域 -->
            <section class="macro-status-section" id="macro-status-section" style="display: none;">
                <div class="macro-status-grid" id="macro-status-grid">
                    <!-- 宏状态卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 其他模块区域 - 下方横向排列 -->
            <section class="modules-grid">
                <div class="module-card" id="segments-module">
                    <div class="card-header">
                        <h3>分段预设</h3>
                        <button class="add-segment-btn" title="添加新分段">+</button>
                    </div>
                    <div class="card-content">
                        <!-- 分段内容将由 JavaScript 动态生成 -->
                    </div>
                </div>

                <div class="module-card" id="effects-module">
                    <div class="card-header">
                        <h3>灯效预设</h3>
                        <div class="header-controls">
                            <label class="preview-toggle">
                                <input type="checkbox" id="preview-mode-toggle">
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">实时预览</span>
                            </label>
                            <button class="add-effect-btn" title="添加新灯效">+</button>
                        </div>
                    </div>
                    <div class="card-content">
                        <!-- 灯效内容将由 JavaScript 动态生成 -->
                    </div>
                </div>

                <div class="module-card" id="scenes-module">
                    <div class="card-header">
                        <h3>场景预设</h3>
                        <button class="add-scene-btn" title="添加新场景">+</button>
                    </div>
                    <div class="card-content">
                        <!-- 场景内容将由 JavaScript 动态生成 -->
                    </div>
                </div>

                <div class="module-card" id="macros-module">
                    <div class="card-header">
                        <h3>自定义宏</h3>
                        <div class="header-buttons">
                            <button class="macro-help-btn" title="配置示例">📖</button>
                            <button class="add-macro-btn" title="添加新宏">+</button>
                        </div>
                    </div>
                    <div class="card-content">
                        <!-- 宏列表区域 -->
                        <div class="macros-list-container">
                            <div class="macros-list-header">
                                <h2>已配置的宏事件</h2>
                            </div>
                            <div class="macros-list" id="macros-list">
                                <!-- 宏列表将由 JavaScript 动态生成 -->
                                <div class="macros-empty">
                                    <div class="macros-empty-icon">⚙️</div>
                                    <div class="macros-empty-text">暂无宏</div>
                                    <div class="macros-empty-hint">点击右上角的 + 号添加新宏</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <div class="footer-info">
                <span>ESP32 Moonraker Monitor v1.0</span>
                <span id="last-update">最后更新: --</span>
            </div>
        </footer>
    </div>

    <!-- 分段编辑模态框 -->
    <div id="segment-modal" class="module-modal-segments">
        <div class="module-modal-segments-content">
            <div class="module-modal-segments-header">
                <h3 id="modal-title" class="module-modal-segments-title">添加新分段</h3>
                <button class="module-modal-segments-close">&times;</button>
            </div>
            <div class="module-modal-segments-body">
                <!-- 统计信息 -->
                <div class="segments-stats">
                    <div class="stat-item">
                        <div class="stat-label">总数</div>
                        <div class="stat-value total" id="modal-total-leds">37</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">已用</div>
                        <div class="stat-value used" id="modal-used-leds">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">未用</div>
                        <div class="stat-value current" id="modal-current-leds">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">剩余</div>
                        <div class="stat-value available" id="modal-remaining-leds">37</div>
                    </div>
                </div>

                <!-- 表单 -->
                <form id="segment-form">
                    <div class="segments-form-group">
                        <label class="segments-form-label" for="segment-name">分段名称</label>
                        <input type="text" id="segment-name" class="segments-form-control" placeholder="例如：客厅灯带" required>
                    </div>

                    <div class="segments-form-row">
                        <div class="segments-form-col">
                            <label class="segments-form-label" for="segment-start">起始灯珠</label>
                            <input type="number" id="segment-start" class="segments-form-control" min="1" max="37" required>
                        </div>
                        <div class="segments-form-col">
                            <label class="segments-form-label" for="segment-end">结束灯珠</label>
                            <input type="number" id="segment-end" class="segments-form-control" min="1" max="37" required>
                        </div>
                    </div>

                    <div class="segments-form-group">
                        <label class="segments-form-label">灯珠数量：<span id="segment-length">10</span>个灯珠</label>
                    </div>
                </form>
            </div>
            <div class="module-modal-segments-footer">
                <button type="button" class="segments-btn segments-btn-secondary" id="segment-cancel-btn">取消</button>
                <button type="submit" form="segment-form" class="segments-btn segments-btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 灯效编辑模态框 -->
    <div id="effect-modal" class="module-modal-effects">
        <div class="module-modal-effects-content">
            <div class="module-modal-effects-header">
                <h3 class="module-modal-effects-title" id="effect-modal-title">添加灯效预设</h3>
                <button class="module-modal-effects-close">&times;</button>
            </div>
            <div class="module-modal-effects-body">
                <form id="effect-form">
                    <div class="effects-form-group">
                        <label class="effects-form-label">预设名称 <span>*</span></label>
                        <input type="text" class="effects-form-control" id="effect-name"
                               placeholder="例如：我的彩虹灯效" required>
                        <div class="effects-form-error" id="effect-name-error"></div>
                    </div>

                    <div class="effects-form-group">
                        <div class="effects-form-label-row">
                            <label class="effects-form-label">灯效类型 <span>*</span></label>
                            <span class="effect-type-description" id="effect-type-description"></span>
                        </div>
                        <select class="effects-form-control" id="effect-type" required>
                            <option value="">请选择灯效类型</option>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                        <div class="effects-form-error" id="effect-type-error"></div>
                    </div>

                    <div class="effects-form-group">
                        <label class="effects-form-label">应用分段 <span>*</span></label>
                        <div class="segments-selection" id="segments-selection">
                            <!-- 分段选项将通过JavaScript动态生成 -->
                        </div>
                        <div class="effects-form-error" id="segments-error"></div>
                    </div>

                    <!-- 参数配置区域 -->
                    <div id="params-container">
                        <!-- 参数将根据灯效类型动态生成 -->
                    </div>
                </form>
            </div>
            <div class="module-modal-effects-footer">
                <button type="button" class="effects-btn effects-btn-secondary" id="effect-cancel-btn">取消</button>
                <button type="submit" form="effect-form" class="effects-btn effects-btn-primary" id="effect-save-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 场景编辑模态框 -->
    <div id="scene-modal" class="module-modal-scenes">
        <div class="module-modal-scenes-content">
            <div class="module-modal-scenes-header">
                <h3 class="module-modal-scenes-title" id="scene-modal-title">添加场景预设</h3>
                <button class="module-modal-scenes-close">&times;</button>
            </div>
            <div class="module-modal-scenes-body">
                <form id="scene-form">
                    <div class="scenes-form-group">
                        <label class="scenes-form-label">场景名称 <span>*</span></label>
                        <input type="text" class="scenes-form-control" id="scene-name"
                               placeholder="例如：打印进度灯效" required>
                        <div class="scenes-form-error" id="scene-name-error"></div>
                    </div>

                    <div class="scenes-form-group">
                        <label class="scenes-form-label">触发事件 <span>*</span></label>
                        <select class="scenes-form-control" id="scene-event-name" required>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                        <div class="scenes-form-error" id="scene-event-error"></div>
                    </div>

                    <div class="scenes-form-group">
                        <label class="scenes-form-label">关联灯效 <span>*</span></label>
                        <select class="scenes-form-control" id="scene-effect-id" required>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                        <div class="scenes-form-hint">选择要执行的灯效</div>
                        <div class="scenes-form-error" id="scene-effect-error"></div>
                    </div>
                </form>
            </div>
            <div class="module-modal-scenes-footer">
                <button type="button" class="scenes-btn scenes-btn-secondary" id="scene-cancel-btn">取消</button>
                <button type="submit" form="scene-form" class="scenes-btn scenes-btn-primary" id="scene-save-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 宏编辑模态框 -->
    <div id="macro-modal" class="module-modal-macros">
        <div class="module-modal-macros-content">
            <div class="module-modal-macros-header">
                <h3 class="module-modal-macros-title" id="modal-title">添加宏事件</h3>
                <button class="module-modal-macros-close">&times;</button>
            </div>
            <div class="module-modal-macros-body">
                <form id="macro-form">
                    <div class="macros-form-group">
                        <label class="macros-form-label">用户显示名称 <span>*</span></label>
                        <input type="text" class="macros-form-control" id="macro-display-name"
                               placeholder="例如：归位" required>
                        <div class="macros-form-error" id="display-name-error"></div>
                    </div>
                    <div class="macros-form-group">
                        <label class="macros-form-label">宏变量名称 <span>*</span></label>
                        <input type="text" class="macros-form-control" id="macro-variable-name"
                               placeholder="例如：homing" required>
                        <div class="macros-form-hint">对应STATUS_VARIABLES中的变量名</div>
                        <div class="macros-form-error" id="variable-name-error"></div>
                    </div>
                </form>
            </div>
            <div class="module-modal-macros-footer">
                <button type="button" class="macros-btn macros-btn-secondary" id="modal-cancel">取消</button>
                <button type="submit" form="macro-form" class="macros-btn macros-btn-primary" id="modal-save">保存</button>
            </div>
        </div>
    </div>

    <!-- 宏配置示例模态框 -->
    <div id="macro-help-modal" class="module-modal-macros">
        <div class="module-modal-macros-content macro-help-modal">
            <div class="module-modal-macros-header">
                <h3 class="module-modal-macros-title">宏使用说明</h3>
            </div>
            <div class="module-modal-macros-body">
                <div class="help-section">
                    <p class="help-description">将状态变量添加到您的列表中</p>
                    <div class="code-block">
                        <pre><code>[gcode_macro STATUS_VARIABLES]    # 这部分全部放在cfg文件中
variable_homing: 'idle'      # homing为监测宏的变量名，可以任意设置（中文除外）
#variable_8888888: 'idle'    # 替换8888888为你要监测的变量名
gcode:
    # 这个宏只用来存储变量，不需要执行</code></pre>
                    </div>
                </div>

                <div class="help-section">
                    <p class="help-description">包装您需要监控的目标宏</p>
                    <p class="help-note">注意：将下面的 YOUR_MACRO_NAME 替换为您实际的宏名</p>
                    <div class="code-block">
                        <pre><code>[gcode_macro YOUR_MACRO_NAME]    # 这是示例宏格式，请勿直接使用
rename_existing: homing
gcode:
    SET_GCODE_VARIABLE MACRO=STATUS_VARIABLES VARIABLE=YOUR_MACRO_NAME VALUE="running"
    # 原始宏代码
    SET_GCODE_VARIABLE MACRO=STATUS_VARIABLES VARIABLE=YOUR_MACRO_NAME VALUE="idle"</code></pre>
                    </div>
                </div>

                <div class="module-modal-macros-footer">
                    <button type="button" class="macros-btn macros-btn-secondary" id="macro-help-close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="printer/printer.js"></script>
    <script src="segments/segments.js"></script>
    <script src="effects/effects.js"></script>
    <script src="scenes/scenes.js"></script>
    <script src="macros/macros.js"></script>
    <script src="script.js"></script>

    <!-- 初始化脚本 -->
    <script>
        // 确保在所有脚本加载后初始化
        window.addEventListener('load', function() {
            console.log('页面完全加载，开始初始化...');

            // 等待一小段时间确保所有脚本都执行完毕
            setTimeout(function() {
                if (window.PrinterManager && typeof window.PrinterManager.init === 'function') {
                    console.log('开始初始化PrinterManager...');
                    window.PrinterManager.init();
                } else {
                    console.error('PrinterManager初始化失败，对象:', window.PrinterManager);
                }
            }, 200);
        });
    </script>
</body>
</html>
