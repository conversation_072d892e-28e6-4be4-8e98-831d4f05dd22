#include "PrinterManager.h"
#include "Config.h"
#include "MacrosManager.h"
#include "ScenesManager.h"

// 初始化静态实例指针
PrinterManager* PrinterManager::instance = nullptr;

/**
 * 构造函数
 */
PrinterManager::PrinterManager(MoonrakerClient& moonrakerClient)
    : moonraker(moonrakerClient), macrosManager(nullptr), scenesManager(nullptr), ws("/ws/printer") {
    
    // 安全地设置全局实例指针
    if (instance == nullptr) {
        instance = this;
    } else {
        Serial.println("警告: 尝试创建多个PrinterManager实例!");
    }
}

/**
 * 析构函数
 */
PrinterManager::~PrinterManager() {
    // 清理实例指针
    if (instance == this) {
        instance = nullptr;
    }
}

/**
 * 初始化PrinterManager
 */
void PrinterManager::begin() {
    Serial.println("初始化 PrinterManager...");
    
    // 设置WebSocket事件处理
    ws.onEvent(staticOnWebSocketEvent);
    
    // 初始化状态
    currentStatus = getStatusFromMoonraker();
    lastStatus = currentStatus;
    
    Serial.println("PrinterManager 初始化完成");
}

/**
 * 主循环处理函数
 * 现在只负责清理WebSocket连接，状态推送改为回调驱动
 */
void PrinterManager::loop() {
    // 清理WebSocket连接
    ws.cleanupClients();
}

/**
 * 状态变化回调函数
 * 当MoonrakerClient接收到新数据时被调用
 */
void PrinterManager::onStatusChanged() {
    // 获取最新状态
    currentStatus = getStatusFromMoonraker();
    currentStatus.timestamp = millis();

    // 检查是否有变化
    if (currentStatus != lastStatus) {
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.println("收到Moonraker数据变化，立即推送给前端...");
#endif

        // 通知ScenesManager状态变化
        if (scenesManager) {
            scenesManager->onPrinterStatusChanged(currentStatus, lastStatus);
        }

        // 如果当前没有活动场景，主动触发场景评估
        // 这解决了ESP32启动时没有启动场景，但打印机已连接的情况
        if (scenesManager && !scenesManager->hasActiveScene()) {
            Serial.println("[PrinterManager] 检测到无活动场景，主动触发场景评估");
            scenesManager->evaluateSceneChange();
        }

        // 转换为JSON并广播
        String statusJSON = statusToJSON(currentStatus);
        broadcastStatus(statusJSON);

        // 更新缓存
        lastStatus = currentStatus;

        // 输出状态变化日志
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.printf("状态: %s, 进度: %.1f%%, 文件: %s\n",
                     currentStatus.state.c_str(),
                     currentStatus.progress * 100,
                     currentStatus.filename.c_str());
        Serial.printf("温度 - 喷嘴: %.1f/%.1f°C, 热床: %.1f/%.1f°C\n",
                     currentStatus.extruderTemp, currentStatus.extruderTarget,
                     currentStatus.bedTemp, currentStatus.bedTarget);
#endif
    }
}

/**
 * 设置MacrosManager引用
 */
void PrinterManager::setMacrosManager(MacrosManager* macrosMgr) {
    macrosManager = macrosMgr;
}

/**
 * 设置ScenesManager引用
 */
void PrinterManager::setScenesManager(ScenesManager* scenesMgr) {
    scenesManager = scenesMgr;
}

/**
 * 宏状态变化通知
 */
void PrinterManager::onMacroStatusChanged() {
    // 当宏状态变化时，立即推送更新给前端
    String statusJSON = getCurrentStatusJSON();
    broadcastStatus(statusJSON);
}

/**
 * 场景状态变化通知
 */
void PrinterManager::onSceneStatusChanged() {
    // 当场景状态变化时，立即推送更新给前端
    String statusJSON = getCurrentStatusJSON();
    broadcastStatus(statusJSON);
}

/**
 * 注册API端点到WebServer
 */
void PrinterManager::registerAPI(AsyncWebServer& server) {
    Serial.println("注册PrinterManager API端点...");
    
    // 注册WebSocket处理器
    server.addHandler(&ws);
    
    // 注册HTTP API端点
    server.on("/api/printer/status", HTTP_GET, 
        [this](AsyncWebServerRequest* request) {
            this->handleGetStatus(request);
        });
    
    server.on("/api/printer/info", HTTP_GET,
        [this](AsyncWebServerRequest* request) {
            this->handleGetInfo(request);
        });
    
    Serial.println("PrinterManager API端点注册完成");
}

/**
 * 获取当前打印机状态JSON
 */
String PrinterManager::getCurrentStatusJSON() {
    currentStatus = getStatusFromMoonraker();
    return statusToJSON(currentStatus);
}

/**
 * 获取当前打印机状态对象
 */
const PrinterStatus& PrinterManager::getCurrentStatus() const {
    return currentStatus;
}

/**
 * 检查是否有客户端连接
 */
bool PrinterManager::hasConnectedClients() {
    return ws.count() > 0;
}

/**
 * 从MoonrakerClient获取最新状态
 */
PrinterStatus PrinterManager::getStatusFromMoonraker() {
    PrinterStatus status;
    
    // 从MoonrakerClient获取数据
    status.state = moonraker.getPrinterState();
    status.progress = moonraker.getPrintProgress();
    status.filename = moonraker.getFilename();
    status.extruderTemp = moonraker.getExtruderTemp();
    status.extruderTarget = moonraker.getExtruderTarget();
    status.bedTemp = moonraker.getBedTemp();
    status.bedTarget = moonraker.getBedTarget();
    status.timestamp = millis();
    
    return status;
}

/**
 * 将状态转换为JSON格式
 */
String PrinterManager::statusToJSON(const PrinterStatus& status) {
    statusDoc.clear();
    
    statusDoc["state"] = status.state;
    statusDoc["progress"] = status.progress;
    statusDoc["filename"] = status.filename;
    
    JsonObject temps = statusDoc.createNestedObject("temperatures");
    JsonObject extruder = temps.createNestedObject("extruder");
    extruder["current"] = status.extruderTemp;
    extruder["target"] = status.extruderTarget;

    JsonObject bed = temps.createNestedObject("bed");
    bed["current"] = status.bedTemp;
    bed["target"] = status.bedTarget;
    
    statusDoc["timestamp"] = status.timestamp;

    // 添加宏状态信息
    if (macrosManager != nullptr) {
        JsonArray macroArray = statusDoc.createNestedArray("macros");
        const auto& macros = macrosManager->getMacros();

        for (const auto& macro : macros) {
            if (macro.enabled) {
                JsonObject macroObj = macroArray.createNestedObject();
                macroObj["id"] = macro.id;
                macroObj["displayName"] = macro.displayName;
                macroObj["variableName"] = macro.variableName;
                macroObj["currentStatus"] = MacroConfig::statusToString(macro.currentStatus);
                macroObj["lastUpdateTime"] = macro.lastUpdateTime;
            }
        }
    }

    String result;
    serializeJson(statusDoc, result);
    return result;
}

/**
 * 向所有连接的WebSocket客户端广播状态更新
 */
void PrinterManager::broadcastStatus(const String& statusJSON) {
    if (ws.count() > 0) {
        ws.textAll(statusJSON);
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.printf("向 %d 个客户端广播状态更新\n", ws.count());
#endif
    } else {
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.println("没有WebSocket客户端连接，跳过广播");
#endif
    }
}

/**
 * WebSocket事件处理函数
 */
void PrinterManager::onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client,
                                     AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch(type) {
        case WS_EVT_CONNECT: {
#if ENABLE_SERIAL_STATUS_OUTPUT
            Serial.printf("WebSocket客户端连接: %u，来自IP: %s\n", client->id(), client->remoteIP().toString().c_str());
#endif
            // 立即发送当前状态给新连接的客户端
            String statusJSON = getCurrentStatusJSON();

            // 确保立即发送，不等待下一次循环
            if (client->canSend()) {
                client->text(statusJSON);
#if ENABLE_SERIAL_STATUS_OUTPUT
                Serial.printf("初始状态已发送给客户端 %u\n", client->id());
#endif
            } else {
#if ENABLE_SERIAL_STATUS_OUTPUT
                Serial.printf("客户端 %u 暂时无法接收数据\n", client->id());
#endif
            }
            break;
        }
            
        case WS_EVT_DISCONNECT:
#if ENABLE_SERIAL_STATUS_OUTPUT
            Serial.printf("WebSocket客户端断开: %u\n", client->id());
#endif
            break;

        case WS_EVT_DATA: {
            AwsFrameInfo* info = (AwsFrameInfo*)arg;
            if(info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
                data[len] = 0;
                String message = (char*)data;
#if ENABLE_SERIAL_STATUS_OUTPUT
                Serial.printf("收到WebSocket消息: %s\n", message.c_str());
#endif
                
                // 处理客户端请求（如果需要的话）
                if (message == "get_status") {
                    client->text(getCurrentStatusJSON());
                }
            }
            break;
        }
        
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}

/**
 * 处理HTTP API请求 - 获取状态
 */
void PrinterManager::handleGetStatus(AsyncWebServerRequest* request) {
    String statusJSON = getCurrentStatusJSON();
    request->send(200, "application/json", statusJSON);
}

/**
 * 处理HTTP API请求 - 获取连接信息
 */
void PrinterManager::handleGetInfo(AsyncWebServerRequest* request) {
    statusDoc.clear();
    statusDoc["connected_clients"] = ws.count();
    statusDoc["moonraker_connected"] = moonraker.isConnected();
    statusDoc["uptime"] = millis();
    
    String result;
    serializeJson(statusDoc, result);
    request->send(200, "application/json", result);
}

/**
 * 静态WebSocket事件处理函数
 */
void PrinterManager::staticOnWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                           AwsEventType type, void* arg, uint8_t* data, size_t len) {
    if (instance) {
        instance->onWebSocketEvent(server, client, type, arg, data, len);
    }
}
