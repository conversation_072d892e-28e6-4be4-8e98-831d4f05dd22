#pragma once

#include <Arduino.h>
#include <FastLED.h>
#include <map>
#include <vector>
#include "Config.h"

/**
 * 灯效参数类型枚举
 */
enum class EffectParamType {
    BRIGHTNESS = 0,    // 亮度 (1-255)
    SPEED = 1,         // 速度 (1-100)
    COLOR = 2,         // 颜色 (RGB值)
    DIRECTION = 3,     // 方向 (0=正向, 1=反向)
    INTENSITY = 4      // 强度 (1-100)
};

/**
 * 灯效类型枚举
 */
enum class EffectType {
    BREATHING = 0,     // 呼吸灯
    RAINBOW = 1,       // 彩虹
    RUNNING = 2,       // 跑马灯
    TEMPERATURE_MAP = 3 // 温度映射
};

/**
 * 灯效参数配置结构
 * 定义每个参数的显示信息和取值范围
 */
struct EffectParamConfig {
    EffectParamType type;        // 参数类型
    const char* displayName;     // 显示名称
    int minValue;               // 最小值
    int maxValue;               // 最大值
    int defaultValue;           // 默认值
    const char* unit;           // 单位
    
    EffectParamConfig(EffectParamType t, const char* name, int min, int max, int def, const char* u = "")
        : type(t), displayName(name), minValue(min), maxValue(max), defaultValue(def), unit(u) {}
};

/**
 * 灯效参数值结构
 * 存储实际的参数值
 */
struct EffectParams {
    uint8_t brightness;           // 亮度 (1-255)
    uint8_t speed;               // 速度 (1-100)
    std::vector<CRGB> colors;    // 颜色数组 (支持多颜色)
    uint8_t direction;           // 方向 (0=正向, 1=反向)
    uint8_t intensity;           // 强度 (1-100)

    // 温度映射专用参数
    bool extruderEnabled;         // 喷嘴温度是否启用
    std::vector<CRGB> extruderColors;  // 喷嘴温度颜色数组
    uint8_t extruderBrightness;   // 喷嘴温度亮度 (1-255)

    bool bedEnabled;              // 热床温度是否启用
    std::vector<CRGB> bedColors;  // 热床温度颜色数组
    uint8_t bedBrightness;        // 热床温度亮度 (1-255)

    // 构造函数 - 使用默认值
    EffectParams() {
        brightness = LED_BRIGHTNESS_DEFAULT;
        speed = LED_SPEED_DEFAULT;
        colors.push_back(CRGB::Red); // 默认红色
        direction = 0;
        intensity = LED_INTENSITY_DEFAULT;

        // 温度映射默认配置
        extruderEnabled = true;
        extruderColors.push_back(CRGB::Red);
        extruderBrightness = LED_BRIGHTNESS_DEFAULT;

        bedEnabled = false;
        bedColors.push_back(CRGB::Blue);
        bedBrightness = LED_BRIGHTNESS_DEFAULT;
    }

    // 构造函数 - 指定值 (兼容旧接口)
    EffectParams(uint8_t b, uint8_t s, CRGB c, uint8_t d = 0, uint8_t i = LED_INTENSITY_DEFAULT)
        : brightness(b), speed(s), direction(d), intensity(i) {
        colors.push_back(c);

        // 温度映射默认配置
        extruderEnabled = true;
        extruderColors.push_back(c);
        extruderBrightness = b;

        bedEnabled = false;
        bedColors.push_back(CRGB::Blue);
        bedBrightness = b;
    }

    // 构造函数 - 多颜色版本
    EffectParams(uint8_t b, uint8_t s, const std::vector<CRGB>& c, uint8_t d = 0, uint8_t i = LED_INTENSITY_DEFAULT)
        : brightness(b), speed(s), colors(c), direction(d), intensity(i) {

        // 温度映射默认配置
        extruderEnabled = true;
        extruderColors = c;
        extruderBrightness = b;

        bedEnabled = false;
        bedColors.push_back(CRGB::Blue);
        bedBrightness = b;
    }
};

/**
 * 灯效信息结构
 * 包含灯效的基本信息和可用参数
 */
struct EffectInfo {
    EffectType type;                              // 灯效类型
    const char* name;                            // 灯效名称
    const char* description;                     // 灯效描述
    std::vector<EffectParamConfig> availableParams; // 可用参数列表
    
    EffectInfo(EffectType t, const char* n, const char* desc, const std::vector<EffectParamConfig>& params)
        : type(t), name(n), description(desc), availableParams(params) {}
};

/**
 * 灯效算法库类
 * 
 * 提供各种灯效的核心算法实现
 * 所有方法都是静态的，不需要实例化
 */
class LightEffect {
public:
    /**
     * 获取所有可用的灯效信息
     * 
     * @return 灯效信息列表
     */
    static std::vector<EffectInfo> getAllEffectInfo();
    
    /**
     * 根据灯效类型获取可用参数配置
     * 
     * @param effectType 灯效类型
     * @return 参数配置列表
     */
    static std::vector<EffectParamConfig> getEffectParams(EffectType effectType);
    
    /**
     * 获取灯效名称
     * 
     * @param effectType 灯效类型
     * @return 灯效名称
     */
    static const char* getEffectName(EffectType effectType);
    
    /**
     * 验证参数值是否在有效范围内
     * 
     * @param effectType 灯效类型
     * @param params 参数值
     * @return 验证结果
     */
    static bool validateParams(EffectType effectType, const EffectParams& params);
    
    // =================================================================
    // 灯效渲染方法
    // =================================================================
    
    /**
     * 渲染呼吸灯效果
     * 
     * @param leds LED数组指针
     * @param startLed 起始LED位置 (从0开始)
     * @param endLed 结束LED位置 (包含)
     * @param params 灯效参数
     * @param frameTime 当前帧时间 (毫秒)
     */
    static void renderBreathing(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime);
    
    /**
     * 渲染彩虹灯效果
     * 
     * @param leds LED数组指针
     * @param startLed 起始LED位置 (从0开始)
     * @param endLed 结束LED位置 (包含)
     * @param params 灯效参数
     * @param frameTime 当前帧时间 (毫秒)
     */
    static void renderRainbow(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime);
    
    /**
     * 渲染跑马灯效果
     *
     * @param leds LED数组指针
     * @param startLed 起始LED位置 (从0开始)
     * @param endLed 结束LED位置 (包含)
     * @param params 灯效参数
     * @param frameTime 当前帧时间 (毫秒)
     */
    static void renderRunning(CRGB* leds, int startLed, int endLed, const EffectParams& params, unsigned long frameTime);

    /**
     * 渲染温度映射效果
     *
     * @param leds LED数组指针
     * @param startLed 起始LED位置 (从0开始)
     * @param endLed 结束LED位置 (包含)
     * @param params 灯效参数
     * @param currentTemp 当前温度 (°C)
     * @param targetTemp 目标温度 (°C)
     * @param frameTime 当前帧时间 (毫秒)
     */
    static void renderTemperatureMap(CRGB* leds, int startLed, int endLed, const EffectParams& params,
                                    float currentTemp, float targetTemp, unsigned long frameTime);
    
    // =================================================================
    // 工具方法
    // =================================================================
    
    /**
     * 将速度值转换为时间间隔
     * 
     * @param speed 速度值 (1-100)
     * @return 时间间隔 (毫秒)
     */
    static unsigned long speedToInterval(uint8_t speed);
    
    /**
     * 应用亮度到颜色
     * 
     * @param color 原始颜色
     * @param brightness 亮度值 (1-255)
     * @return 应用亮度后的颜色
     */
    static CRGB applyBrightness(const CRGB& color, uint8_t brightness);
    
    /**
     * 限制参数值在有效范围内
     *
     * @param value 参数值
     * @param minVal 最小值
     * @param maxVal 最大值
     * @return 限制后的值
     */
    static int constrainValue(int value, int minVal, int maxVal);

private:
    // 灯效配置映射表
    static const std::map<EffectType, std::vector<EffectParamConfig>> EFFECT_PARAM_MAP;
    
    // 灯效信息映射表
    static const std::map<EffectType, EffectInfo> EFFECT_INFO_MAP;
    
    /**
     * 初始化灯效配置映射表
     */
    static std::map<EffectType, std::vector<EffectParamConfig>> initEffectParamMap();
    
    /**
     * 初始化灯效信息映射表
     */
    static std::map<EffectType, EffectInfo> initEffectInfoMap();
};
